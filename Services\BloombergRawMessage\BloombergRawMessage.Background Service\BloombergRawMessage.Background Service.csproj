﻿<Project Sdk="Microsoft.NET.Sdk.Worker">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>dotnet-SiepeBackgroundServiceTemplate-f87c6f51-d196-47e8-874d-ee26a63b869f</UserSecretsId>
	  <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\Libraries\DBUtility\DBUtility.Common\DBUtility.Common.csproj" />
    <ProjectReference Include="..\..\..\Libraries\DBUtility\DBUtility.Extensions\DBUtility.Extensions.csproj" />
    <ProjectReference Include="..\..\..\Libraries\DBUtility\DBUtility.v1\DBUtility.v1.csproj" />
    <ProjectReference Include="..\..\..\Libraries\Infrastructure\HealthChecks\Siepe.Infrastructure.HealthChecks.Extensions.Logging\Siepe.Infrastructure.HealthChecks.Extensions.Logging.csproj" />
    <ProjectReference Include="..\..\..\Libraries\Infrastructure\HealthChecks\Siepe.Infrastructure.HealthChecks.Extensions\Siepe.Infrastructure.HealthChecks.Extensions.csproj" />
    <ProjectReference Include="..\..\..\Libraries\Infrastructure\Logging\Siepe.Infrastructure.Logging.Extensions\Siepe.Infrastructure.Logging.Extensions.csproj" />
    <ProjectReference Include="..\..\..\Libraries\Infrastructure\Logging\Siepe.Infrastructure.Logging.QueryLogging.Extensions\Siepe.Infrastructure.Logging.QueryLogging.Extensions.csproj" />
    <ProjectReference Include="..\..\..\Libraries\Infrastructure\Logging\Siepe.Infrastructure.Logging.QueryLogging\Siepe.Infrastructure.Logging.QueryLogging.csproj" />
    <ProjectReference Include="..\..\..\Libraries\Infrastructure\PubSub\Siepe.Infrastructure.PubSub.Common\Siepe.Infrastructure.PubSub.Common.csproj" />
    <ProjectReference Include="..\..\..\Libraries\Infrastructure\PubSub\Siepe.Infrastructure.PubSub.RabbitMq\Siepe.Infrastructure.PubSub.RabbitMq.csproj" />
    <ProjectReference Include="..\..\..\Libraries\Infrastructure\Siepe.Infrastructure.Extensions\Siepe.Infrastructure.Extensions.csproj" />
    <ProjectReference Include="..\..\..\Libraries\Logging\Siepe.Infrastructure.Logging\Siepe.Infrastructure.Logging\Siepe.Infrastructure.Logging.csproj" />
    <ProjectReference Include="..\..\..\Libraries\Serialization\JsonSerializer\JsonSerializer.csproj" />
    <ProjectReference Include="..\..\..\Libraries\Siepe.NetCore.Infrastructure\Siepe.NetCore.Infrastructure\Siepe.NetCore.Infrastructure.csproj" />
  </ItemGroup>
</Project>
